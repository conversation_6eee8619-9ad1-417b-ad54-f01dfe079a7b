<template>
  <div
    class="event-handler-wrapper"
    @click="handleEvent('click', $event)"
    @dblclick="handleEvent('dblclick', $event)"
    @mouseenter="handleEvent('mouseenter', $event)"
    @mouseleave="handleEvent('mouseleave', $event)"
  >
    <slot></slot>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { onMounted, onBeforeUnmount } from 'vue'
import { dataManager } from '../../utils/dataManager'

const props = defineProps({
  component: {
    type: Object,
    required: true
  },
  isPreview: {
    type: Boolean,
    default: false
  },
  pageComponents: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['toggleComponent'])
const router = useRouter()

// Custom event handlers for form components
const customEventHandlers = {
  input: (event) => handleEvent('input', event),
  change: (event) => handleEvent('change', event),
  blur: (event) => handleEvent('blur', event),
  focus: (event) => handleEvent('focus', event)
}

// Add event listeners for custom events
onMounted(() => {
  // Add listeners for custom events from form components
  Object.keys(customEventHandlers).forEach(eventType => {
    document.addEventListener(`component:${eventType}`, customEventHandlers[eventType])
  })
})

// Remove event listeners before component is destroyed
onBeforeUnmount(() => {
  // Remove listeners for custom events
  Object.keys(customEventHandlers).forEach(eventType => {
    document.removeEventListener(`component:${eventType}`, customEventHandlers[eventType])
  })
})

// Handle event
const handleEvent = (eventType, event) => {
  console.log(`EventHandlerWrapper: Handling ${eventType} event for component:`, props.component.type, props.component.id)

  // Only handle events in preview mode
  if (!props.isPreview) {
    console.log('EventHandlerWrapper: Not in preview mode, ignoring event')
    return
  }

  // Check if component has events
  if (!props.component.events) {
    console.log('EventHandlerWrapper: Component has no events property')
    return
  }

  if (!Array.isArray(props.component.events)) {
    console.log('EventHandlerWrapper: Component events is not an array:', props.component.events)
    return
  }

  console.log('EventHandlerWrapper: Component events:', JSON.stringify(props.component.events))

  // Find matching events
  const matchingEvents = props.component.events.filter(e => e.type === eventType)
  console.log(`EventHandlerWrapper: Found ${matchingEvents.length} matching events for type ${eventType}:`, JSON.stringify(matchingEvents))

  // Execute each matching event
  matchingEvents.forEach((eventConfig, index) => {
    console.log(`EventHandlerWrapper: Executing event ${index + 1}/${matchingEvents.length}:`, JSON.stringify(eventConfig))

    // For custom events from form components, the actual event is in event.detail
    // For native events, use the event directly
    const eventData = event.detail || event

    // Prevent default for navigation events
    if (eventConfig.action && eventConfig.action.type === 'navigate') {
      event.preventDefault()
      event.stopPropagation()
    }

    executeAction(eventConfig.action, eventData)
  })
}

// Execute action based on action type
const executeAction = (action, event) => {
  console.log('EventHandlerWrapper: Executing action:', JSON.stringify(action))

  if (!action || !action.type) {
    console.warn('EventHandlerWrapper: Invalid action:', action)
    return
  }

  // Add additional debugging for navigation actions
  if (action.type === 'navigate') {
    console.log('EventHandlerWrapper: Navigation action details:', {
      navigationType: action.navigationType,
      pageId: action.pageId,
      url: action.url
    })
  }

  switch (action.type) {
    case 'navigate':
      console.log('EventHandlerWrapper: Handling navigate action')
      handleNavigateAction(action)
      break
    case 'message':
      console.log('EventHandlerWrapper: Handling message action')
      handleMessageAction(action)
      break
    case 'toggleComponent':
      console.log('EventHandlerWrapper: Handling toggleComponent action')
      handleToggleComponentAction(action)
      break
    case 'api':
      console.log('EventHandlerWrapper: Handling api action')
      handleApiAction(action)
      break
    case 'setVariable':
      console.log('EventHandlerWrapper: Handling setVariable action')
      handleSetVariableAction(action)
      break
    case 'javascript':
      console.log('EventHandlerWrapper: Handling javascript action')
      handleJavaScriptAction(action)
      break
    default:
      console.warn('EventHandlerWrapper: Unknown action type:', action.type)
  }
}

// Handle navigate action
const handleNavigateAction = (action) => {
  console.log('EventHandlerWrapper: handleNavigateAction with action:', JSON.stringify(action))

  // 检查导航类型，支持 'page' 和 '页面' 两种值
  if ((action.navigationType === 'page' || action.navigationType === '页面') && action.pageId) {
    // Navigate to internal page
    // Get the current project ID from the route
    const currentRoute = router.currentRoute.value
    console.log('EventHandlerWrapper: Current route:', currentRoute.fullPath, 'Params:', JSON.stringify(currentRoute.params))

    const projectId = currentRoute.params.projectId
    console.log('EventHandlerWrapper: Extracted projectId:', projectId)

    if (projectId) {
      const targetUrl = `/preview/${projectId}/${action.pageId}`
      console.log(`EventHandlerWrapper: Navigating to page: ${targetUrl}`)

      // 检查是否导航到当前页面
      if (currentRoute.fullPath === targetUrl) {
        console.log('EventHandlerWrapper: Already on target page, refreshing instead')
        ElMessage.info('已经在当前页面，正在刷新...')

        // 清除本地存储的页面配置，确保重新加载
        localStorage.removeItem('previewPageConfig')

        // 刷新当前页面
        window.location.reload()
      } else {
        try {
          // 清除本地存储的页面配置，确保加载新页面时从服务器获取最新数据
          localStorage.removeItem('previewPageConfig')

          // Use router.push with an object to ensure proper route resolution
          router.push({
            name: 'Preview',
            params: {
              projectId: projectId,
              pageId: action.pageId
            }
          })
          console.log('EventHandlerWrapper: Navigation initiated with params:', {
            projectId: projectId,
            pageId: action.pageId
          })
        } catch (error) {
          console.error('EventHandlerWrapper: Navigation error:', error)
          ElMessage.error(`导航失败: ${error.message}`)
        }
      }
    } else {
      console.error('EventHandlerWrapper: Cannot navigate: missing project ID in current route')
      ElMessage.error('导航失败: 无法确定当前项目')
    }
  } else if (action.navigationType === 'url' && action.url) {
    // Navigate to external URL
    console.log(`EventHandlerWrapper: Opening external URL: ${action.url}`)
    window.open(action.url, '_blank')
  } else {
    console.warn('EventHandlerWrapper: Invalid navigation action configuration:', JSON.stringify(action))
    console.warn('EventHandlerWrapper: navigationType =', action.navigationType, 'pageId =', action.pageId, 'url =', action.url)
    ElMessage.warning('导航配置不完整')
  }
}

// Handle message action
const handleMessageAction = (action) => {
  if (!action.message) {
    return
  }

  const messageType = action.messageType || 'info'
  ElMessage({
    message: action.message,
    type: messageType
  })
}

// Handle toggle component action
const handleToggleComponentAction = (action) => {
  if (!action.targetComponentId) {
    return
  }

  emit('toggleComponent', {
    componentId: action.targetComponentId,
    operation: action.operation || 'toggle'
  })
}

// Handle API action
const handleApiAction = async (action) => {
  if (!action.apiUrl) {
    console.warn('API URL is required for API action')
    ElMessage.warning('API URL未配置')
    return
  }

  const method = action.apiMethod || 'get'
  let params = {}

  // Parse API params if provided
  if (action.apiParams) {
    try {
      params = JSON.parse(action.apiParams)
    } catch (e) {
      console.error('Failed to parse API params:', e)
      ElMessage.error('API参数格式错误')
      return
    }
  }

  console.log(`EventHandlerWrapper: Making ${method.toUpperCase()} request to ${action.apiUrl}`, { params })

  try {
    // 使用数据管理器执行API请求
    const result = await dataManager.executeApi({
      url: action.apiUrl,
      method: method,
      params: params,
      dataKey: action.dataKey || `api_${Date.now()}`
    })

    if (result.success) {
      console.log('EventHandlerWrapper: API request successful:', result.data)

      // 如果配置了数据绑定目标，将数据绑定到指定组件
      if (action.dataBindingTarget) {
        handleDataBinding(action.dataBindingTarget, result.data, result.apiKey)
      }

      // 如果配置了变量名，将数据存储到变量中
      if (action.resultVariable) {
        dataManager.setVariable(action.resultVariable, result.data)
      }

      // Show success message if configured
      if (action.showSuccessMessage) {
        const message = action.successMessage || '操作成功'
        ElMessage.success(message)
      }

      // 触发数据更新事件，通知页面组件刷新
      document.dispatchEvent(new CustomEvent('apiDataUpdate', {
        detail: {
          apiKey: result.apiKey,
          data: result.data,
          action: action
        }
      }))

    } else {
      console.error('EventHandlerWrapper: API request failed:', result.error)
      ElMessage.error(action.errorMessage || `操作失败: ${result.error}`)
    }

  } catch (error) {
    console.error('EventHandlerWrapper: API request error:', error)
    ElMessage.error('网络请求失败')
  }
}

// Handle data binding
const handleDataBinding = (target, data, apiKey) => {
  console.log('EventHandlerWrapper: Handling data binding:', { target, data, apiKey })

  if (!target || !target.componentId) {
    console.warn('EventHandlerWrapper: Invalid data binding target')
    return
  }

  // 发送数据绑定事件
  document.dispatchEvent(new CustomEvent('componentDataBinding', {
    detail: {
      componentId: target.componentId,
      property: target.property || 'data',
      data: data,
      apiKey: apiKey
    }
  }))
}

// 改进变量设置处理函数
const handleSetVariableAction = (action) => {
  if (!action.variableName) {
    console.warn('Variable name is required for setVariable action')
    return
  }

  let variableValue = action.variableValue

  // 检查变量值是否包含表达式
  if (typeof variableValue === 'string' && variableValue.includes('{{') && variableValue.includes('}}')) {
    const expressionMatch = variableValue.match(/\{\{(.+?)\}\}/)
    if (expressionMatch) {
      const expression = expressionMatch[1].trim()

      // 获取当前变量值
      const currentValue = dataManager.getVariable(action.variableName) || 0

      // 创建计算上下文
      const context = {
        variables: dataManager.variables,
        apiData: dataManager.apiData,
        pageData: dataManager.pageData,
        // 添加当前变量到上下文，方便引用
        [action.variableName]: currentValue
      }

      try {
        // 计算表达式
        const evalFunction = new Function(
          ...Object.keys(context),
          `return ${expression}`
        )

        variableValue = evalFunction(...Object.values(context))
        console.log(`Evaluated expression "${expression}" to:`, variableValue)
      } catch (error) {
        console.error(`Failed to evaluate expression "${expression}":`, error)
      }
    }
  }

  // 设置变量值
  dataManager.setVariable(action.variableName, variableValue)
  console.log(`EventHandlerWrapper: Set variable ${action.variableName} to:`, variableValue)

  // 触发变量更新事件
  document.dispatchEvent(new CustomEvent('variableUpdate', {
    detail: {
      name: action.variableName,
      value: variableValue
    }
  }))
}

// Handle JavaScript action
const handleJavaScriptAction = (action) => {
  if (!action.jsCode) {
    console.warn('JavaScript code is required for javascript action')
    return
  }

  try {
    // Create a new Function to execute the code
    // This provides a sandboxed environment with access to window
    const executeCode = new Function(action.jsCode)
    executeCode()
  } catch (error) {
    console.error('Failed to execute JavaScript code:', error)
    ElMessage.error('JavaScript执行失败')
  }
}
</script>

<style scoped>
.event-handler-wrapper {
  width: 100%;
  height: 100%;
}
</style>
