<template>
  <div class="preview-container">
    <div class="preview-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">Back to Editor</el-button>
        <el-button icon="HomeFilled" @click="goHome" type="primary" plain>首页</el-button>
        <h2>{{ pageConfig.title || 'Preview' }}</h2>
      </div>
      <div class="header-right">
        <div class="device-info">
          <span class="device-size">{{ deviceSize }}</span>
        </div>
        <div class="zoom-control">
          <el-button-group>
            <el-button :disabled="scale <= 0.5" @click="decreaseScale" icon="ZoomOut" />
            <el-button type="primary">{{ scalePercent }}</el-button>
            <el-button :disabled="scale >= 1.5" @click="increaseScale" icon="ZoomIn" />
          </el-button-group>
        </div>
        <div class="orientation-control" v-if="currentDevice !== 'desktop'">
          <el-tooltip :content="isLandscape ? '切换为竖屏' : '切换为横屏'" placement="top">
            <el-button @click="toggleOrientation" :icon="isLandscape ? 'Cellphone' : 'Expand'" />
          </el-tooltip>
        </div>
        <el-radio-group v-model="currentDevice" size="large">
          <el-radio-button label="desktop">
            <el-icon><Monitor /></el-icon>
            <span class="device-label">桌面端</span>
          </el-radio-button>
          <el-radio-button label="tablet">
            <el-icon><Platform /></el-icon>
            <span class="device-label">平板端</span>
          </el-radio-button>
          <el-radio-button label="mobile">
            <el-icon><Cellphone /></el-icon>
            <span class="device-label">移动端</span>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="preview-content">
      <div class="device-container" :style="{ transform: `scale(${scale})` }">
        <!-- Orientation indicator -->
        <div v-if="isLandscape && currentDevice !== 'desktop'" class="orientation-indicator">
          <el-icon><Expand /></el-icon>
          <span>横屏模式</span>
        </div>
        <!-- Device frame -->
        <div class="device-frame" :class="[deviceClass, { 'landscape': isLandscape && currentDevice !== 'desktop' }]">
          <!-- Device screen -->
          <div class="device-screen">
            <div v-if="pageConfig.components && pageConfig.components.length > 0" class="preview-page" :style="pageConfig.styles || {}">
              <component-renderer
                v-for="component in visibleComponents"
                :key="component.id"
                :component="component"
                :is-preview="true"
                :page-components="pageConfig.components"
                @toggle-component="handleToggleComponent"
              />
            </div>
            <div v-else class="empty-preview" :style="pageConfig.styles || {}">
              <p>No components to preview</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { HomeFilled, Monitor, Cellphone, ZoomIn, ZoomOut, Expand, Platform } from '@element-plus/icons-vue'
import { pageApi } from '../../api'
import ComponentRenderer from '../../components/editor/ComponentRenderer.vue'

const route = useRoute()
const router = useRouter()

// Current device for preview
const currentDevice = ref('desktop') // Default to desktop

// Watch for device changes to reset landscape mode
watch(currentDevice, () => {
  // Reset to portrait mode when changing devices
  isLandscape.value = false
})

// Scale factor for zooming
const scale = ref(1) // Default scale is 1 (100%)

// Landscape mode flag
const isLandscape = ref(false) // Default to portrait mode

// Computed scale percentage for display
const scalePercent = computed(() => {
  return `${Math.round(scale.value * 100)}%`
})

// Increase scale (zoom in)
const increaseScale = () => {
  if (scale.value < 1.5) {
    scale.value = Math.round((scale.value + 0.1) * 10) / 10 // Round to 1 decimal place
  }
}

// Decrease scale (zoom out)
const decreaseScale = () => {
  if (scale.value > 0.5) {
    scale.value = Math.round((scale.value - 0.1) * 10) / 10 // Round to 1 decimal place
  }
}

// Toggle device orientation (landscape/portrait)
const toggleOrientation = () => {
  isLandscape.value = !isLandscape.value
}

// Computed device class based on current device
const deviceClass = computed(() => {
  return `device-${currentDevice.value}`
})

// Device dimensions
const deviceDimensions = {
  desktop: { width: 1200, height: 800 },
  tablet: { width: 768, height: 1024 },
  mobile: { width: 375, height: 667 }
}

// Computed device size string
const deviceSize = computed(() => {
  const dimensions = deviceDimensions[currentDevice.value]
  if (!dimensions) return ''

  // If in landscape mode, swap width and height (except for desktop)
  if (isLandscape.value && currentDevice.value !== 'desktop') {
    return `${dimensions.height} × ${dimensions.width}`
  } else {
    return `${dimensions.width} × ${dimensions.height}`
  }
})

// Component visibility state
const componentVisibility = ref({})

// Computed visible components
const visibleComponents = computed(() => {
  if (!pageConfig.value.components) {
    return []
  }

  return pageConfig.value.components.filter(component => {
    // 确保组件对象有效
    if (!component || !component.id || !component.type) {
      console.warn('Preview: Invalid component found:', component)
      return false
    }

    // If component has no visibility state, default to visible
    return componentVisibility.value[component.id] !== false
  })
})

// Handle toggle component action
const handleToggleComponent = (payload) => {
  const { componentId, operation } = payload

  // If component doesn't exist in visibility state, initialize it as visible
  if (componentVisibility.value[componentId] === undefined) {
    componentVisibility.value[componentId] = true
  }

  // Update visibility based on operation
  switch (operation) {
    case 'show':
      componentVisibility.value[componentId] = true
      break
    case 'hide':
      componentVisibility.value[componentId] = false
      break
    case 'toggle':
      componentVisibility.value[componentId] = !componentVisibility.value[componentId]
      break
  }
}

// Page configuration
const pageConfig = ref({
  name: '',
  title: '',
  path: '',
  components: []
})

// Load page data
const loadPageData = async () => {
  console.log('Preview: Loading page data')

  // 获取页面ID和项目ID
  const pageId = route.params.pageId
  const projectId = route.params.projectId
  console.log('Preview: Loading page, pageId:', pageId, 'projectId:', projectId)

  // 如果是新页面或者没有页面ID，尝试从本地存储加载
  if (!pageId || pageId === 'new') {
    console.log('Preview: New page or no pageId, trying localStorage')
    const storedConfig = localStorage.getItem('previewPageConfig')
    if (storedConfig) {
      try {
        pageConfig.value = JSON.parse(storedConfig)
        console.log('Preview: Loaded config from localStorage for new page:', pageConfig.value.title)
        return
      } catch (e) {
        console.error('Preview: Failed to parse stored page config:', e)
      }
    }
    return
  }

  // 对于已有页面，优先从服务器加载最新数据
  try {
    console.log('Preview: Calling pageApi.getPageDetail with pageId:', pageId)
    const pageData = await pageApi.getPageDetail(pageId)

    if (pageData) {
      console.log('Preview: Received page data from API:', pageData.name, pageData.title)
      pageConfig.value.name = pageData.name
      pageConfig.value.title = pageData.title
      pageConfig.value.path = pageData.path
      pageConfig.value.id = pageData.id // 保存页面ID，用于后续比较

      // Parse components if stored as string
      if (typeof pageData.config === 'string' && pageData.config) {
        try {
          console.log('Preview: Parsing page config string')
          const config = JSON.parse(pageData.config)
          pageConfig.value.components = config.components || []
          pageConfig.value.styles = config.styles || {}
          console.log('Preview: Parsed components count:', pageConfig.value.components.length)
          console.log('Preview: Parsed styles:', pageConfig.value.styles)
        } catch (e) {
          console.error('Preview: Failed to parse page config:', e)
          pageConfig.value.components = []
        }
      } else if (pageData.config && pageData.config.components) {
        console.log('Preview: Using components from config object')
        pageConfig.value.components = pageData.config.components
        pageConfig.value.styles = pageData.config.styles || {}
        console.log('Preview: Components count:', pageConfig.value.components.length)
        console.log('Preview: Styles:', pageConfig.value.styles)
      } else {
        console.warn('Preview: No components found in page data')
        pageConfig.value.components = []
      }

      // 更新本地存储，以便在需要时使用
      localStorage.setItem('previewPageConfig', JSON.stringify(pageConfig.value))
      console.log('Preview: Updated localStorage with latest page data')
    } else {
      console.error('Preview: Page not found from API')

      // 如果服务器没有找到页面，尝试从本地存储加载
      const storedConfig = localStorage.getItem('previewPageConfig')
      if (storedConfig) {
        try {
          const parsedConfig = JSON.parse(storedConfig)
          // 只有当存储的配置ID与请求的页面ID匹配时才使用
          if (parsedConfig.id == pageId) {
            console.log('Preview: Using stored config as fallback for pageId:', pageId)
            pageConfig.value = parsedConfig
            return
          }
        } catch (e) {
          console.error('Preview: Failed to parse stored page config:', e)
        }
      }

      ElMessage.error('Page not found')
      goBack()
    }
  } catch (error) {
    console.error('Preview: Failed to load page data from server:', error)

    // 如果服务器请求失败，尝试从本地存储加载
    const storedConfig = localStorage.getItem('previewPageConfig')
    if (storedConfig) {
      try {
        const parsedConfig = JSON.parse(storedConfig)
        // 只有当存储的配置ID与请求的页面ID匹配时才使用
        if (parsedConfig.id == pageId) {
          console.log('Preview: Using stored config due to server error for pageId:', pageId)
          pageConfig.value = parsedConfig
          return
        }
      } catch (e) {
        console.error('Preview: Failed to parse stored page config:', e)
      }
    }

    ElMessage.error('Failed to load page data')
    goBack()
  }
}

// Go back to editor
const goBack = () => {
  const projectId = route.params.projectId
  const pageId = route.params.pageId
  console.log('Preview: Going back to editor, projectId:', projectId, 'pageId:', pageId)

  if (projectId && pageId) {
    console.log(`Preview: Navigating to /editor/${projectId}/${pageId}`)
    router.push(`/editor/${projectId}/${pageId}`)
  } else {
    console.log('Preview: Missing projectId or pageId, navigating to /project')
    router.push('/project')
  }
}

// Go to home page
const goHome = () => {
  console.log('Preview: Navigating to dashboard')
  router.push('/dashboard')
}

// 使用 watch 监听路由参数变化
// 当路由参数变化时，清除本地存储并重新加载数据
watch(
  () => route.params.pageId,
  (newPageId, oldPageId) => {
    if (newPageId && newPageId !== oldPageId) {
      console.log('Preview: Page ID changed from', oldPageId, 'to', newPageId)
      console.log('Preview: Clearing localStorage and reloading data')
      localStorage.removeItem('previewPageConfig')
      loadPageData()
    }
  }
)

// Load page data on mount
onMounted(() => {
  console.log('Preview: Component mounted, route params:', route.params)
  loadPageData()
})
</script>

<style scoped>
.preview-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 0 15px;
  font-size: 18px;
}

.preview-content {
  flex: 1;
  overflow: auto;
  background-color: #f5f7fa;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.device-info {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.device-size {
  font-size: 14px;
  color: #606266;
  font-family: monospace;
}

.device-label {
  margin-left: 5px;
}

.device-container {
  position: relative;
  transition: all 0.3s ease;
  transform-origin: center center;
}

.zoom-control, .orientation-control {
  display: flex;
  align-items: center;
}

.orientation-indicator {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #409eff;
  color: white;
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  z-index: 10;
}

.device-frame {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  margin: 0 auto;
}

/* Desktop device */
.device-desktop {
  width: 1200px;
  height: 800px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
}

/* Tablet device */
.device-tablet {
  width: 768px;
  height: 1024px;
  border-radius: 20px;
  border: 16px solid #e4e7ed;
}

/* Mobile device */
.device-mobile {
  width: 375px;
  height: 667px;
  border-radius: 36px;
  border: 16px solid #000;
  position: relative;
}

/* Mobile device notch */
.device-mobile::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 16px;
  background-color: #000;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  z-index: 10;
}

.device-screen {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* Landscape mode styles */
.device-frame.landscape {
  transform: rotate(90deg);
}

.device-tablet.landscape {
  width: 1024px;
  height: 768px;
}

.device-mobile.landscape {
  width: 667px;
  height: 375px;
}

.preview-page {
  min-height: 100%;
  width: 100%;
  /* 确保组件容器能够正确显示背景色 */
  display: flex;
  flex-direction: column;
}

.empty-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  width: 100%;
  color: #909399;
}
</style>
