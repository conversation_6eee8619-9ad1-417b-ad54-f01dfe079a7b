# ????
spring.application.name=low-code

# ?????
server.port=8080

# ?????
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456

# Druid?????
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20

# MyBatis-Plus??
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.web.lowcode.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.id-type=auto

# Swagger??
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/v3/api-docs

# JWT??
jwt.secret=QVRXRUJMb3dDb2RlUGxhdGZvcm1TZWNyZXRLZXlGb3JKV1RUb2tlbkdlbmVyYXRpb24=
jwt.token-validity-in-seconds=86400

# Spring Security??
spring.security.filter.order=10
spring.security.user.name=admin
spring.security.user.password=admin

# MinIO??
minio.endpoint=http://localhost:9000
minio.access-key=minioadmin
minio.secret-key=minioadmin
minio.bucket-name=lowcode-images
minio.image-size-limit=10485760
minio.allowed-content-types=image/jpeg,image/jpg,image/png,image/gif,image/bmp,image/webp

# 文件存储配置
# 存储类型选择：minio(MinIO存储) / local(本地存储) / auto(自动选择，已废弃)
file.storage.type=local
file.storage.local.base-path=./uploads
file.storage.local.url-prefix=http://localhost:8080/api/files
# 是否启用回退机制（仅在type=auto时有效，建议设为false使用明确的存储方式）
file.storage.fallback-to-local=false
# 存储方式说明：
# - minio: 强制使用MinIO存储，MinIO不可用时直接报错
# - local: 强制使用本地存储
# - auto: 自动选择（不推荐，建议明确指定存储方式）

# ??????
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ????
logging.level.org.springframework.security=DEBUG
logging.level.com.web.lowcode=DEBUG
logging.level.org.springframework.security.crypto.bcrypt=TRACE
